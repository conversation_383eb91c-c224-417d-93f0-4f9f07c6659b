"""
可转债市场数据分析系统
基于Context7增强架构模式，提供可转债日均振幅分析功能
"""

import akshare as ak
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
import time
import json
from pathlib import Path

# 导入增强系统的核心模块
from config import get_config, StockConfig
from stock_data_fetcher import RateLimiter, FetchResult
from data_validator import ValidationResult
from utils import (
    setup_logging, format_duration, format_number,
    validate_date_range, create_output_directory, 
    save_dataframe_multiple_formats, create_progress_bar,
    calculate_progress_eta
)


@dataclass
class 可转债配置:
    """可转债分析专用配置"""
    最小数据要求: int = 1  # 由于只有当前数据，降低要求
    振幅阈值: float = 0.0
    前N名数量: int = 10
    必需列名: List[str] = None

    def __post_init__(self):
        if self.必需列名 is None:
            # 基于实际API返回的列名
            self.必需列名 = ['现价', '涨跌幅', '转股溢价率', '到期税前收益']


@dataclass
class 可转债分析结果:
    """可转债分析结果数据结构"""
    可转债代码: str
    可转债名称: str
    日均振幅百分比: float
    交易天数: int
    平均价格: float
    价格波动率: float
    总收益率: float
    数据质量评分: float


class 可转债数据获取器:
    """
    可转债数据获取器，提供重试机制和错误处理
    """
    
    def __init__(self, 配置: Optional[StockConfig] = None):
        """
        初始化可转债数据获取器
        
        Args:
            配置: 系统配置，如果为None则使用全局配置
        """
        self.配置 = 配置 or get_config()
        self.日志器 = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.限流器 = RateLimiter(self.配置.api.rate_limit_delay)
        self.会话统计 = {
            '总请求数': 0,
            '成功请求数': 0,
            '失败请求数': 0,
            '重试次数': 0,
            '开始时间': datetime.now()
        }
    
    def 获取可转债列表(self) -> FetchResult:
        """
        获取所有可转债列表
        
        Returns:
            FetchResult: 包含可转债列表或错误信息的结果
        """
        开始时间 = time.time()
        
        for 尝试次数 in range(1, self.配置.api.max_retries + 1):
            try:
                self.限流器.wait()
                self.会话统计['总请求数'] += 1
                
                self.日志器.debug(f"获取可转债列表 (第{尝试次数}次尝试)")
                
                # 使用akshare获取可转债数据
                可转债列表 = ak.bond_cb_jsl()
                
                if 可转债列表.empty:
                    raise ValueError("获取到空的可转债列表")
                
                self.会话统计['成功请求数'] += 1
                持续时间 = time.time() - 开始时间
                
                self.日志器.info(f"成功获取 {len(可转债列表)} 只可转债")
                return FetchResult(
                    success=True,
                    data=可转债列表,
                    attempts=尝试次数,
                    duration=持续时间
                )
                
            except Exception as e:
                self.会话统计['失败请求数'] += 1
                错误信息 = f"第{尝试次数}次尝试失败: {str(e)}"
                self.日志器.warning(错误信息)
                
                if 尝试次数 < self.配置.api.max_retries:
                    self.会话统计['重试次数'] += 1
                    等待时间 = self._计算退避延迟(尝试次数)
                    self.日志器.info(f"将在 {等待时间:.2f} 秒后重试...")
                    time.sleep(等待时间)
                else:
                    持续时间 = time.time() - 开始时间
                    最终错误 = f"获取可转债列表失败，已尝试 {尝试次数} 次。最后错误: {str(e)}"
                    self.日志器.error(最终错误)
                    return FetchResult(
                        success=False,
                        error=最终错误,
                        attempts=尝试次数,
                        duration=持续时间
                    )
        
        return FetchResult(success=False, error="重试循环中的意外错误")
    
    def 获取可转债当前数据(self) -> FetchResult:
        """
        获取可转债当前市场数据（包含价格信息）

        Returns:
            FetchResult: 包含可转债市场数据或错误信息的结果
        """
        开始时间 = time.time()

        for 尝试次数 in range(1, self.配置.api.max_retries + 1):
            try:
                self.限流器.wait()
                self.会话统计['总请求数'] += 1

                self.日志器.debug(f"获取可转债市场数据 (第{尝试次数}次尝试)")

                # 获取可转债市场数据，这个API包含了价格信息
                市场数据 = ak.bond_cb_jsl()

                if 市场数据.empty:
                    raise ValueError("获取到空的可转债市场数据")

                self.会话统计['成功请求数'] += 1
                持续时间 = time.time() - 开始时间

                self.日志器.debug(f"成功获取可转债市场数据，包含 {len(市场数据)} 只可转债")
                return FetchResult(
                    success=True,
                    data=市场数据,
                    attempts=尝试次数,
                    duration=持续时间
                )

            except Exception as e:
                self.会话统计['失败请求数'] += 1
                错误信息 = f"第{尝试次数}次尝试失败: {str(e)}"
                self.日志器.debug(错误信息)

                if 尝试次数 < self.配置.api.max_retries:
                    self.会话统计['重试次数'] += 1
                    等待时间 = self._计算退避延迟(尝试次数)
                    time.sleep(等待时间)
                else:
                    持续时间 = time.time() - 开始时间
                    最终错误 = f"获取可转债市场数据失败，已尝试 {尝试次数} 次。最后错误: {str(e)}"
                    return FetchResult(
                        success=False,
                        error=最终错误,
                        attempts=尝试次数,
                        duration=持续时间
                    )

        return FetchResult(success=False, error="重试循环中的意外错误")
    
    def _计算退避延迟(self, 尝试次数: int) -> float:
        """
        计算指数退避延迟时间
        
        Args:
            尝试次数: 当前尝试次数（从1开始）
            
        Returns:
            延迟秒数
        """
        import random
        基础延迟 = self.配置.api.rate_limit_delay * (self.配置.api.retry_backoff_factor ** (尝试次数 - 1))
        # 添加随机抖动避免雷群效应
        抖动 = random.uniform(0.5, 1.5)
        return 基础延迟 * 抖动
    
    def 获取会话统计(self) -> Dict[str, Any]:
        """获取当前会话的统计信息"""
        持续时间 = (datetime.now() - self.会话统计['开始时间']).total_seconds()
        成功率 = (
            self.会话统计['成功请求数'] / self.会话统计['总请求数']
            if self.会话统计['总请求数'] > 0 else 0
        )
        
        return {
            **self.会话统计,
            '会话持续时间': 持续时间,
            '成功率': 成功率,
            '每秒请求数': self.会话统计['总请求数'] / 持续时间 if 持续时间 > 0 else 0
        }


class 可转债数据验证器:
    """
    可转债数据验证器，提供数据质量检查和清洗功能
    """
    
    def __init__(self, 配置: 可转债配置):
        """
        初始化验证器
        
        Args:
            配置: 可转债分析配置
        """
        self.配置 = 配置
        self.日志器 = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def 验证可转债数据(self, 数据: pd.DataFrame, 可转债代码: str) -> ValidationResult:
        """
        验证单只可转债的数据
        
        Args:
            数据: 可转债数据DataFrame
            可转债代码: 可转债代码
            
        Returns:
            ValidationResult: 验证结果
        """
        错误列表 = []
        警告列表 = []
        
        if 数据.empty:
            错误列表.append(f"可转债 {可转债代码}: 无可用数据")
            return ValidationResult(is_valid=False, errors=错误列表, warnings=警告列表)
        
        # 检查必需列
        缺失列 = [列 for 列 in self.配置.必需列名 if 列 not in 数据.columns]
        if 缺失列:
            错误列表.append(f"可转债 {可转债代码}: 缺少必需列: {缺失列}")
            return ValidationResult(is_valid=False, errors=错误列表, warnings=警告列表)

        清洗后数据 = 数据.copy()
        
        # 数据类型验证和清洗
        数值列 = ['现价', '涨跌幅', '转股溢价率', '到期税前收益']
        for 列名 in 数值列:
            if 列名 in 清洗后数据.columns:
                # 转换为数值类型
                非数值数据 = pd.to_numeric(清洗后数据[列名], errors='coerce').isna()
                if 非数值数据.any():
                    警告列表.append(f"可转债 {可转债代码}: {列名} 列存在非数值数据")
                    清洗后数据[列名] = pd.to_numeric(清洗后数据[列名], errors='coerce')

                # 价格和比率范围验证
                if 列名 == '现价':
                    无效价格 = (清洗后数据[列名] <= 0) | (清洗后数据[列名] > 1000)
                    if 无效价格.any():
                        警告列表.append(f"可转债 {可转债代码}: {列名} 列存在 {无效价格.sum()} 个无效价格")
                        清洗后数据 = 清洗后数据[~无效价格]
                elif 列名 == '涨跌幅':
                    # 涨跌幅通常在-50%到50%之间
                    无效涨跌幅 = (清洗后数据[列名] < -50) | (清洗后数据[列名] > 50)
                    if 无效涨跌幅.any():
                        警告列表.append(f"可转债 {可转债代码}: {列名} 列存在 {无效涨跌幅.sum()} 个异常值")

        # 移除关键数据缺失的行
        关键列 = ['现价']
        清洗前行数 = len(清洗后数据)
        清洗后数据 = 清洗后数据.dropna(subset=关键列)
        清洗后行数 = len(清洗后数据)

        if 清洗前行数 != 清洗后行数:
            警告列表.append(f"可转债 {可转债代码}: 移除了 {清洗前行数 - 清洗后行数} 行关键数据缺失的记录")

        # 最终验证 - 对于当前数据，只需要有数据即可
        是否有效 = len(错误列表) == 0 and len(清洗后数据) >= self.配置.最小数据要求

        if not 是否有效 and len(错误列表) == 0:
            错误列表.append(
                f"可转债 {可转债代码}: 清洗后数据不足 "
                f"({len(清洗后数据)} < {self.配置.最小数据要求} 所需行数)"
            )
        
        # 计算数据质量评分
        质量评分 = self._计算数据质量评分(清洗后数据) if 是否有效 else 0.0
        
        return ValidationResult(
            is_valid=是否有效,
            errors=错误列表,
            warnings=警告列表,
            cleaned_data=清洗后数据 if 是否有效 else None,
            stats={'数据质量评分': 质量评分, '清洗后行数': len(清洗后数据)}
        )
    
    def _计算数据质量评分(self, 数据: pd.DataFrame) -> float:
        """
        计算数据质量评分
        
        Args:
            数据: 清洗后的数据
            
        Returns:
            质量评分 (0-1之间)
        """
        try:
            if 数据.empty:
                return 0.0
            
            评分 = 1.0
            
            # 检查缺失值比例
            缺失比例 = 数据.isnull().sum().sum() / (len(数据) * len(数据.columns))
            评分 -= 缺失比例 * 0.3
            
            # 检查价格一致性
            if all(列 in 数据.columns for 列 in ['最高价', '最低价', '开盘价', '收盘价']):
                # 检查是否有异常的价格变动（超过50%的日变动）
                if '收盘价' in 数据.columns and len(数据) > 1:
                    极端变动 = (数据['收盘价'].pct_change().abs() > 0.5).sum()
                    评分 -= (极端变动 / len(数据)) * 0.2
            
            return max(0.0, 评分)
            
        except Exception:
            return 0.5  # 如果计算失败，返回默认评分


class 可转债分析器:
    """
    可转债分析器，计算振幅和其他技术指标
    """

    def __init__(self, 配置: 可转债配置):
        """
        初始化分析器

        Args:
            配置: 可转债分析配置
        """
        self.配置 = 配置
        self.日志器 = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    def 计算当日表现指标(self, 数据: pd.DataFrame, 可转债代码: str) -> Optional[float]:
        """
        基于当前市场数据计算可转债的综合表现指标
        由于只有当前数据，我们使用涨跌幅和转股溢价率来计算综合指标

        Args:
            数据: 可转债当前市场数据DataFrame
            可转债代码: 可转债代码

        Returns:
            综合表现指标，如果计算失败则返回None
        """
        try:
            if 数据.empty:
                self.日志器.warning(f"可转债 {可转债代码}: 数据为空，无法计算表现指标")
                return None

            # 使用涨跌幅的绝对值作为活跃度指标
            if '涨跌幅' in 数据.columns:
                涨跌幅 = 数据['涨跌幅'].iloc[0] if len(数据) > 0 else 0
                活跃度指标 = abs(涨跌幅)

                self.日志器.debug(f"可转债 {可转债代码}: 当日涨跌幅 {涨跌幅:.2f}%，活跃度指标 {活跃度指标:.2f}%")
                return 活跃度指标
            else:
                self.日志器.warning(f"可转债 {可转债代码}: 缺少涨跌幅数据")
                return None

        except Exception as e:
            self.日志器.error(f"可转债 {可转债代码}: 表现指标计算错误: {e}")
            return None

    def 计算转股溢价率(self, 数据: pd.DataFrame, 可转债代码: str) -> Optional[float]:
        """
        获取转股溢价率

        Args:
            数据: 可转债数据DataFrame
            可转债代码: 可转债代码

        Returns:
            转股溢价率，如果计算失败则返回None
        """
        try:
            if '转股溢价率' in 数据.columns and len(数据) > 0:
                转股溢价率 = 数据['转股溢价率'].iloc[0]
                return float(转股溢价率) if pd.notna(转股溢价率) else None
            return None

        except Exception as e:
            self.日志器.error(f"可转债 {可转债代码}: 转股溢价率获取错误: {e}")
            return None

    def 计算市场统计(self, 数据: pd.DataFrame, 可转债代码: str) -> Dict[str, float]:
        """
        计算可转债市场统计信息

        Args:
            数据: 可转债数据DataFrame
            可转债代码: 可转债代码

        Returns:
            市场统计字典
        """
        try:
            统计信息 = {}

            if len(数据) > 0:
                行数据 = 数据.iloc[0]

                # 基本价格信息
                if '现价' in 数据.columns:
                    统计信息['当前价格'] = float(行数据['现价']) if pd.notna(行数据['现价']) else 0.0

                # 涨跌幅
                if '涨跌幅' in 数据.columns:
                    统计信息['涨跌幅'] = float(行数据['涨跌幅']) if pd.notna(行数据['涨跌幅']) else 0.0

                # 转股溢价率
                if '转股溢价率' in 数据.columns:
                    统计信息['转股溢价率'] = float(行数据['转股溢价率']) if pd.notna(行数据['转股溢价率']) else 0.0

                # 到期税前收益
                if '到期税前收益' in 数据.columns:
                    统计信息['到期税前收益'] = float(行数据['到期税前收益']) if pd.notna(行数据['到期税前收益']) else 0.0

                # 成交额
                if '成交额' in 数据.columns:
                    统计信息['成交额'] = float(行数据['成交额']) if pd.notna(行数据['成交额']) else 0.0

                # 换手率
                if '换手率' in 数据.columns:
                    统计信息['换手率'] = float(行数据['换手率']) if pd.notna(行数据['换手率']) else 0.0

            return 统计信息

        except Exception as e:
            self.日志器.error(f"可转债 {可转债代码}: 市场统计计算错误: {e}")
            return {}

    def 分析可转债(self, 数据: pd.DataFrame, 可转债代码: str, 可转债名称: str,
                数据质量评分: float = 0.0) -> Optional[可转债分析结果]:
        """
        对单只可转债进行综合分析

        Args:
            数据: 可转债数据DataFrame
            可转债代码: 可转债代码
            可转债名称: 可转债名称
            数据质量评分: 数据质量评分

        Returns:
            可转债分析结果，如果分析失败则返回None
        """
        try:
            # 计算当日表现指标（使用涨跌幅绝对值）
            表现指标 = self.计算当日表现指标(数据, 可转债代码)
            if 表现指标 is None:
                self.日志器.warning(f"可转债 {可转债代码}: 表现指标计算失败")
                return None

            # 检查表现指标阈值
            if 表现指标 < self.配置.振幅阈值:
                self.日志器.debug(
                    f"可转债 {可转债代码}: 表现指标低于阈值 "
                    f"({表现指标:.2f}% < {self.配置.振幅阈值}%)"
                )
                return None

            # 计算其他指标
            转股溢价率 = self.计算转股溢价率(数据, 可转债代码) or 0.0
            市场统计 = self.计算市场统计(数据, 可转债代码)

            return 可转债分析结果(
                可转债代码=可转债代码,
                可转债名称=可转债名称,
                日均振幅百分比=表现指标,  # 使用表现指标作为"振幅"
                交易天数=1,  # 当前数据只有1天
                平均价格=市场统计.get('当前价格', 0.0),
                价格波动率=转股溢价率,  # 使用转股溢价率作为"波动率"
                总收益率=市场统计.get('涨跌幅', 0.0),  # 使用涨跌幅作为"收益率"
                数据质量评分=数据质量评分
            )

        except Exception as e:
            self.日志器.error(f"可转债 {可转债代码}: 分析失败: {e}")
            return None

    def 批量分析(self, 可转债市场数据: pd.DataFrame) -> List[可转债分析结果]:
        """
        批量分析可转债市场数据并返回排序结果

        Args:
            可转债市场数据: 包含所有可转债当前市场数据的DataFrame

        Returns:
            按表现指标排序的分析结果列表
        """
        结果列表 = []

        for 索引, 行数据 in 可转债市场数据.iterrows():
            try:
                可转债代码 = 行数据['代码']
                可转债名称 = 行数据['转债名称']

                # 将单行数据转换为DataFrame进行分析
                单行数据 = pd.DataFrame([行数据])

                # 验证数据
                验证结果 = self.数据验证器.验证可转债数据(单行数据, 可转债代码)

                if 验证结果.is_valid:
                    数据质量评分 = 验证结果.stats.get('数据质量评分', 0.0) if 验证结果.stats else 0.0

                    结果 = self.分析可转债(验证结果.cleaned_data, 可转债代码, 可转债名称, 数据质量评分)
                    if 结果:
                        结果列表.append(结果)
                else:
                    self.日志器.debug(f"可转债 {可转债代码} 验证失败: {验证结果.errors}")

            except Exception as e:
                self.日志器.debug(f"处理可转债数据时出错: {e}")
                continue

        # 按表现指标降序排序并取前N名
        结果列表.sort(key=lambda x: x.日均振幅百分比, reverse=True)
        前N名结果 = 结果列表[:self.配置.前N名数量]

        self.日志器.info(
            f"分析完成: 从 {len(结果列表)} 只有效可转债中选出前 {len(前N名结果)} 名"
        )

        return 前N名结果


class 可转债分析系统:
    """
    可转债分析系统主类，协调数据获取、验证、分析和输出
    """

    def __init__(self, 配置: Optional[StockConfig] = None):
        """
        初始化可转债分析系统

        Args:
            配置: 系统配置，如果为None则使用全局配置
        """
        self.系统配置 = 配置 or get_config()
        self.可转债配置 = 可转债配置()

        # 设置日志
        self.日志器 = setup_logging(self.系统配置.logging)
        self.日志器.info("可转债分析系统已初始化")

        # 初始化组件
        self.数据获取器 = 可转债数据获取器(self.系统配置)
        self.数据验证器 = 可转债数据验证器(self.可转债配置)
        self.分析器 = 可转债分析器(self.可转债配置)
        # 将验证器传递给分析器
        self.分析器.数据验证器 = self.数据验证器

        # 会话跟踪
        self.会话开始时间 = datetime.now()
        self.会话统计 = {
            '处理可转债数': 0,
            '成功可转债数': 0,
            '验证错误数': 0,
            '分析结果数': 0
        }

    def 测试数据获取(self, 年份: int = 2025, 月份: int = 6) -> bool:
        """
        测试可转债数据获取功能

        Args:
            年份: 测试年份
            月份: 测试月份

        Returns:
            测试是否成功
        """
        self.日志器.info("开始测试可转债数据获取功能...")

        try:
            # 获取可转债当前市场数据
            市场数据结果 = self.数据获取器.获取可转债当前数据()

            if not 市场数据结果.success:
                self.日志器.error(f"获取可转债市场数据失败: {市场数据结果.error}")
                return False

            可转债市场数据 = 市场数据结果.data
            self.日志器.info(f"成功获取 {len(可转债市场数据)} 只可转债的市场数据")

            # 测试前几只可转债的数据验证
            测试数量 = min(3, len(可转债市场数据))
            成功数量 = 0

            for i in range(测试数量):
                try:
                    行数据 = 可转债市场数据.iloc[i]
                    可转债代码 = 行数据['代码']
                    可转债名称 = 行数据['转债名称']

                    self.日志器.info(f"测试可转债 {可转债代码} ({可转债名称}) ({i+1}/{测试数量})")

                    # 验证数据
                    单行数据 = pd.DataFrame([行数据])
                    验证结果 = self.数据验证器.验证可转债数据(单行数据, 可转债代码)

                    if 验证结果.is_valid:
                        self.日志器.info(f"✓ 可转债 {可转债代码}: 数据验证通过")
                        成功数量 += 1
                    else:
                        self.日志器.warning(f"✗ 可转债 {可转债代码}: 数据验证失败 - {验证结果.errors}")

                except Exception as e:
                    self.日志器.warning(f"✗ 可转债测试失败: {e}")

            成功率 = 成功数量 / 测试数量 * 100 if 测试数量 > 0 else 0
            self.日志器.info(f"测试完成: {成功数量}/{测试数量} 成功 ({成功率:.1f}%)")

            return 成功率 >= 50  # 如果至少50%成功则认为测试通过

        except Exception as e:
            self.日志器.error(f"测试失败: {e}")
            return False

    def 获取前N名振幅可转债(self, 年份: int = 2025, 月份: int = 6,
                      最大处理数量: Optional[int] = None) -> pd.DataFrame:
        """
        获取指定期间日均振幅前N名的可转债

        Args:
            年份: 分析年份
            月份: 分析月份
            最大处理数量: 最大处理的可转债数量（None表示处理全部）

        Returns:
            包含前N名可转债的DataFrame
        """
        self.日志器.info(f"开始分析 {年份}年{月份}月 可转债振幅")

        try:
            # 获取可转债当前市场数据
            self.日志器.info("获取可转债市场数据...")
            市场数据结果 = self.数据获取器.获取可转债当前数据()

            if not 市场数据结果.success:
                self.日志器.error(f"获取可转债市场数据失败: {市场数据结果.error}")
                return pd.DataFrame()

            可转债市场数据 = 市场数据结果.data

            # 限制处理数量
            if 最大处理数量:
                可转债市场数据 = 可转债市场数据.head(最大处理数量)

            self.日志器.info(f"将分析 {len(可转债市场数据)} 只可转债")

            # 分析可转债
            self.日志器.info("分析可转债表现指标...")
            分析结果列表 = self.分析器.批量分析(可转债市场数据)

            if not 分析结果列表:
                self.日志器.warning("没有生成分析结果")
                return pd.DataFrame()

            # 转换为DataFrame
            结果数据 = []
            for 结果 in 分析结果列表:
                结果数据.append({
                    '可转债代码': 结果.可转债代码,
                    '可转债名称': 结果.可转债名称,
                    '日均振幅(%)': round(结果.日均振幅百分比, 2),
                    '交易天数': 结果.交易天数,
                    '平均价格': round(结果.平均价格, 2),
                    '价格波动率(%)': round(结果.价格波动率, 2),
                    '总收益率(%)': round(结果.总收益率, 2),
                    '数据质量评分': round(结果.数据质量评分, 2)
                })

            结果DataFrame = pd.DataFrame(结果数据)

            # 更新会话统计
            self.会话统计.update({
                '成功可转债数': len(可转债市场数据),
                '验证错误数': len(可转债市场数据) - len(分析结果列表),
                '分析结果数': len(分析结果列表)
            })

            return 结果DataFrame

        except Exception as e:
            self.日志器.error(f"分析失败: {e}", exc_info=True)
            return pd.DataFrame()

    def 保存结果(self, 结果DataFrame: pd.DataFrame, 输出目录: Optional[str] = None) -> Dict[str, str]:
        """
        保存分析结果到多种格式

        Args:
            结果DataFrame: 结果DataFrame
            输出目录: 输出目录，如果为None则使用配置默认值

        Returns:
            格式到保存路径的映射字典
        """
        if 结果DataFrame.empty:
            self.日志器.warning("没有结果需要保存")
            return {}

        try:
            # 创建输出目录
            if 输出目录 is None:
                输出目录 = self.系统配置.output.output_dir

            输出路径 = create_output_directory(输出目录)

            # 生成文件名
            时间戳 = datetime.now().strftime("%Y%m%d_%H%M%S") if self.系统配置.output.include_timestamp else ""
            文件名模板 = "可转债振幅分析_前{n}名_{timestamp}"

            文件名 = 文件名模板.format(
                n=self.可转债配置.前N名数量,
                timestamp=时间戳
            )

            基础路径 = 输出路径 / 文件名

            # 保存为多种格式
            保存的文件 = save_dataframe_multiple_formats(
                结果DataFrame, str(基础路径), self.系统配置.output.save_formats
            )

            # 记录保存的文件
            for 格式, 路径 in 保存的文件.items():
                self.日志器.info(f"结果已保存 ({格式}): {路径}")

            return 保存的文件

        except Exception as e:
            self.日志器.error(f"保存结果失败: {e}")
            return {}

    def 获取会话摘要(self) -> Dict[str, Any]:
        """获取当前会话的摘要信息"""
        持续时间 = (datetime.now() - self.会话开始时间).total_seconds()
        获取器统计 = self.数据获取器.获取会话统计()

        return {
            '会话持续时间': format_duration(持续时间),
            '会话统计': self.会话统计,
            '获取器统计': 获取器统计,
            '配置摘要': {
                'API限流延迟': self.系统配置.api.rate_limit_delay,
                '最大重试次数': self.系统配置.api.max_retries,
                '前N名数量': self.可转债配置.前N名数量,
                '最小数据要求': self.可转债配置.最小数据要求
            }
        }


def main():
    """主执行函数，提供CLI接口"""
    import argparse
    import sys

    解析器 = argparse.ArgumentParser(
        description="可转债市场数据分析系统 - Context7增强架构",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python convertible_bond_analyzer.py --test
  python convertible_bond_analyzer.py --year 2025 --month 6
  python convertible_bond_analyzer.py --year 2024 --month 12 --max-bonds 30
  python convertible_bond_analyzer.py --config custom_config.json
        """
    )

    解析器.add_argument('--test', action='store_true',
                       help='仅运行数据获取测试')
    解析器.add_argument('--year', type=int, default=2025,
                       help='分析年份 (默认: 2025)')
    解析器.add_argument('--month', type=int, default=6,
                       help='分析月份 (默认: 6)')
    解析器.add_argument('--max-bonds', type=int,
                       help='最大处理可转债数量')
    解析器.add_argument('--config', type=str,
                       help='配置文件路径')
    解析器.add_argument('--output-dir', type=str,
                       help='结果输出目录')
    解析器.add_argument('--log-level', type=str,
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    解析器.add_argument('--json-logs', action='store_true',
                       help='启用JSON结构化日志')

    参数 = 解析器.parse_args()

    try:
        # 加载配置
        配置 = None
        if 参数.config:
            from config import StockConfig
            配置 = StockConfig.load_from_file(参数.config)

        # 使用命令行参数覆盖配置
        if 配置 is None:
            配置 = get_config()

        if 参数.log_level:
            配置.logging.level = 参数.log_level
        if 参数.json_logs:
            配置.logging.enable_json_logging = True

        # 初始化系统
        系统 = 可转债分析系统(配置)

        # 运行测试（如果请求）
        if 参数.test:
            系统.日志器.info("运行可转债数据获取测试...")
            测试成功 = 系统.测试数据获取(年份=参数.year, 月份=参数.month)

            if 测试成功:
                系统.日志器.info("✓ 测试成功完成")
                return 0
            else:
                系统.日志器.error("✗ 测试失败")
                return 1

        # 运行完整分析
        系统.日志器.info("开始可转债振幅分析...")

        结果DataFrame = 系统.获取前N名振幅可转债(
            年份=参数.year,
            月份=参数.month,
            最大处理数量=参数.max_bonds
        )

        if 结果DataFrame.empty:
            系统.日志器.error("没有生成分析结果")
            return 1

        # 显示结果
        print(f"\n{参数.year}年{参数.month}月可转债日均振幅前{len(结果DataFrame)}名:")
        print("=" * 100)
        print(结果DataFrame.to_string(index=False))

        # 保存结果
        保存的文件 = 系统.保存结果(结果DataFrame, 参数.output_dir)

        if 保存的文件:
            print(f"\n结果已保存:")
            for 格式, 路径 in 保存的文件.items():
                print(f"  {格式.upper()}: {路径}")

        # 显示会话摘要
        摘要 = 系统.获取会话摘要()
        print(f"\n会话摘要:")
        print(f"  处理时间: {摘要['会话持续时间']}")
        print(f"  处理可转债: {摘要['会话统计']['处理可转债数']}")
        print(f"  成功获取: {摘要['会话统计']['成功可转债数']}")
        print(f"  分析结果: {摘要['会话统计']['分析结果数']}")
        if 摘要['获取器统计']['总请求数'] > 0:
            print(f"  成功率: {摘要['获取器统计']['成功率']:.1%}")

        系统.日志器.info("可转债分析成功完成")
        return 0

    except KeyboardInterrupt:
        print("\n分析被用户中断")
        return 1
    except Exception as e:
        logging.getLogger(__name__).error(f"分析失败: {e}", exc_info=True)
        return 1


# 向后兼容函数
def 测试可转债数据获取(年份=2025, 月份=6):
    """向后兼容的测试函数"""
    系统 = 可转债分析系统()
    return 系统.测试数据获取(年份, 月份)


def 获取前10名振幅可转债(年份=2025, 月份=6):
    """向后兼容的分析函数"""
    系统 = 可转债分析系统()
    结果DataFrame = 系统.获取前N名振幅可转债(年份, 月份, 最大处理数量=50)
    return 结果DataFrame


if __name__ == "__main__":
    import sys
    sys.exit(main())
